version: '3.8'

services:
  # Backend API service
  api:
    image: ${ECR_REGISTRY}/yendorcats-api:${IMAGE_TAG:-latest}
    container_name: yendorcats-api-production
    restart: unless-stopped
    ports:
      - "5003:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - AWS__Region=us-west-004
      - AWS__UseCredentialsFromSecrets=false
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME:-yendor}
      - AWS__S3__UseDirectS3Urls=true
      - AWS__S3__ServiceUrl=https://s3.us-west-004.backblazeb2.com
      - AWS__S3__PublicUrl=https://f004.backblazeb2.com/file/${AWS_S3_BUCKET_NAME:-yendor}/{key}
      - AWS__S3__UseCdn=true
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEY}
      - AWS__S3__KeyPrefix=YendorCats-General-SiteAccess/
      - B2_APPLICATION_KEY_ID=${B2_APPLICATION_KEY_ID}
      - B2_APPLICATION_KEY=${B2_APPLICATION_KEY}
      - B2_BUCKET_ID=${B2_BUCKET_ID}
      - ConnectionStrings__DefaultConnection=Server=db;Database=YendorCats;User=${MYSQL_USER};Password=${MYSQL_PASSWORD};Port=3306;
      - JwtSettings__Secret=${YENDOR_JWT_SECRET}
    volumes:
      - api-data:/app/data
      - api-logs:/app/Logs
    depends_on:
      - db
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - yendorcats-network

  # MariaDB Database
  db:
    image: mariadb:10.11
    container_name: yendorcats-db-production
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=YendorCats
    volumes:
      - mariadb-data:/var/lib/mysql
    networks:
      - yendorcats-network
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # File Upload Service
  uploader:
    image: ${ECR_REGISTRY}/yendorcats-uploader:${IMAGE_TAG:-latest}
    container_name: yendorcats-uploader-production
    restart: unless-stopped
    ports:
      - "5002:80"
    environment:
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME:-yendor}
      - AWS_S3_REGION=us-west-004
      - AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
      - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
      - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
      - API_BASE_URL=http://api
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - yendorcats-network

  # Frontend with Nginx
  frontend:
    image: ${ECR_REGISTRY}/yendorcats-frontend:${IMAGE_TAG:-latest}
    container_name: yendorcats-frontend-production
    ports:
      - "80:80"
    # Using image-based frontend with cache-busted assets; volumes removed for immutability
    depends_on:
      - api
      - uploader
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - yendorcats-network

networks:
  yendorcats-network:
    driver: bridge
    name: yendorcats-production

volumes:
  api-data:
    driver: local
  api-logs:
    driver: local
  mariadb-data:
    driver: local
