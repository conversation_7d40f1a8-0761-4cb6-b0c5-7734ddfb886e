#!/bin/bash

echo "Unlocking and mounting encrypted SD card with write permissions..."
echo "Password hint: sd card mac bw"
echo ""

# First unmount if already mounted
if mount | grep -q "/Volumes/virtualpocket"; then
    echo "Unmounting existing mount..."
    diskutil unmount /dev/disk7s1 2>/dev/null || true
    diskutil unmount /dev/disk7s2 2>/dev/null || true
fi

# Unlock the volume
echo "Unlocking encrypted volume..."
diskutil apfs unlockVolume /dev/disk7s1

# Check if mount is read-only
if mount | grep "/Volumes/virtualpocket" | grep -q "read-only"; then
    echo ""
    echo "Volume mounted as read-only. Attempting to remount with write permissions..."
    
    # Get the mount point
    MOUNT_POINT="/Volumes/virtualpocket"
    
    # Try to remount with sudo
    echo "You may need to enter your admin password for sudo access:"
    sudo mount -uw "$MOUNT_POINT" 2>/dev/null || {
        echo "Standard remount failed. Trying alternative approach..."
        
        # Unmount and remount
        diskutil unmount /dev/disk7s1
        sudo mkdir -p "$MOUNT_POINT"
        sudo mount -t apfs -o rw,nobrowse /dev/disk7s1 "$MOUNT_POINT"
    }
fi

# Verify mount status
echo ""
echo "Current mount status:"
mount | grep "/Volumes/virtualpocket"

# Test write access
echo ""
echo "Testing write access..."
if touch "/Volumes/virtualpocket/.write_test" 2>/dev/null; then
    rm "/Volumes/virtualpocket/.write_test"
    echo "✓ Write access confirmed!"
else
    echo "✗ Write access denied. The volume may still be read-only."
    echo ""
    echo "Alternative: You can try disabling System Integrity Protection (SIP) temporarily:"
    echo "1. Restart your Mac and hold Command+R to enter Recovery Mode"
    echo "2. Open Terminal from Utilities menu"
    echo "3. Run: csrutil disable"
    echo "4. Restart normally and try this script again"
    echo "5. Remember to re-enable SIP afterward with: csrutil enable"
fi
